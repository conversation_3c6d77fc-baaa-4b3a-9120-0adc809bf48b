from typing import Any

from app.modules.engine_health.engine_health_service import EngineHealthService
from app.context.utils import rename_vat


# TODO test with frontend
class EngineHealthLogic:
    def __init__(self, engine_health_service: EngineHealthService):
        self.engine_health_service = engine_health_service

    async def engine_health(self, owner_vat: str, vessel_imo: int) -> Any:
        return await self.engine_health_service.engine_health(**rename_vat(**locals()))
