from fastapi import APIRouter, Depends
from auth import owner_vat_check

from app.modules.engine_health.engine_health_logic import EngineHealthLogic
from app.modules.engine_health.engine_health_service import EngineHealthService
from app.context.context import get_context

router = APIRouter(tags=["Engine Health"])

get_engine_health_params, get_engine_health_logic = get_context(
    EngineHealthLogic,
    EngineHealthService,
)


# TODO test endpoint when we have it in the frontend
@router.get("/engine-health", dependencies=[Depends(owner_vat_check)])
async def engine_health(
    params: dict = Depends(get_engine_health_params),
    logic: EngineHealthLogic = Depends(get_engine_health_logic),
):
    return await logic.engine_health(**params)
