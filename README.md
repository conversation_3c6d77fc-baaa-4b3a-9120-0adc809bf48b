# Backend Next
## Getting Started

### Installation

1. **Clone the repository**
   ```bash
   git clone https://gitlab.frugaltech.lan/my-data/backend-next.git
   cd backend-next
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv venv
   # On Windows
   venv\Scripts\activate
   # On Linux/Mac
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r app/config/requirements.txt
   ```

4. **Configure environment variables**
   Create a `.env` file in the project root:
   ```env
   JWT_SECRET=your-jwt-secret-key
   API_HOST=0.0.0.0
   API_PORT=8769
   API_RELOAD=true
   REDIS_API_URL=http://your-redis-api-url
   REDIS_API_TOKEN=your-redis-api-token
   ```

5. **Run the application**
   ```bash
   python run.py
   ```

The API will be available at `http://localhost:8769` with interactive documentation at `http://localhost:8769/docs`.

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `JWT_SECRET` | Secret key for JWT token signing | Required |
| `API_HOST` | Server host address | `0.0.0.0` |
| `API_PORT` | Server port | `8769` |
| `API_RELOAD` | Enable auto-reload in development | `true` |
| `REDIS_API_URL` | Redis API service URL | Required |
| `REDIS_API_TOKEN` | Redis API authentication token | Required |

### Configuration Files

- **`app/config/settings.py`**: Application settings management
- **`app/config/frugaltech.config`**: Frugal configuration file
- **`app/config/requirements.txt`**: Python dependencies

## Core Components

- **Modules** (`app/modules/`): Feature-specific business logic organized by domain
- **Services** (`app/services/`): External service integrations and data access layers
- **Context System** (`app/context/`): Dependency injection and request parameter management
- **Configuration** (`app/config/`): Application settings and environment configuration
- **Authentication** (`auth.py`): JWT-based authentication and authorization

## Module Structure

Each module follows a consistent three-layer pattern:
- **Router**: FastAPI route definitions and HTTP handling
- **Logic**: Business logic and domain operations
- **Service**: Data access and external service integration

## Context System

The context system (`app/context/`) is a key architectural component that provides dependency injection and request parameter management for the application.

### How the Context System Works

The context system consists of several components:

1. **`get_context()` Function**: Creates dependency injection setup for modules
2. **`get_dependencies()` Function**: Handles automatic dependency resolution
3. **Route Input Decorators**: Manages request parameter extraction

### Context Usage Example

```python
from app.context.context import get_context
from app.context.utils import route_input

# Set up dependency injection for a module
get_module_params, get_module_logic = get_context(
    ModuleLogic,        # Business logic class
    ModuleService,      # Data service class (optional)
    DataController,     # Additional helper classes
    Measurements,
)

@router.post("/endpoint")
@route_input(include_body=True, headers_to_include=["X-Tenant-Owner-Vat"])
async def endpoint(
    params: dict = Depends(get_module_params),
    logic: ModuleLogic = Depends(get_module_logic),
):
    return await logic.process(**params)
```

### Context Features

- **Automatic Dependency Injection**: Resolves constructor dependencies automatically
- **Request Parameter Extraction**: Gathers query params, headers, and body data
- **Type-Safe Dependencies**: Uses Python type hints for dependency resolution
- **Flexible Configuration**: Supports optional services and multiple helper classes

## Services

Services (`app/services/`) handle external integrations and data access. The current implementation includes:

### Redis API Service

The Redis API service provides a client for interacting with Redis-based data storage:

- **Base Client** (`redis_api_client_base.py`): Core HTTP client functionality
- **API Client** (`redis_api_client.py`): Redis-specific operations
- **Configuration** (`redis_api_client_config.py`): Environment-based settings
- **Error Handling** (`redis_api_client_error.py`): Custom exception types

### Service Usage

```python
from app.services.redis_api.redis_api_client import RedisApi

class MyService:
    def __init__(self, redis_client: RedisApi = None):
        self.redis = redis_client or RedisApi()

    async def get_data(self, **params):
        return await self.redis.call(
            path="data_endpoint",
            method="POST",
            json=params,
        )
```

## Setting Up New Modules

To create a new module, follow this standardized structure:

### 1. Create Module Directory

```
app/modules/your_module/
├── your_module_router.py    # FastAPI routes
├── your_module_logic.py     # Business logic
└── your_module_service.py   # Data access (optional)
```

### 2. Implement the Service (Optional)

```python
# your_module_service.py
from app.services.redis_api.redis_api_client import RedisApi

class YourModuleService:
    def __init__(self, redis_client: RedisApi = None):
        self.redis = redis_client or RedisApi()

    async def get_data(self, **params):
        return await self.redis.call(
            path="your_endpoint",
            **params,
        )
```

### 3. Implement the Logic

```python
# your_module_logic.py
from app.context.utils import select_keys

class YourModuleLogic:
    def __init__(self, your_module_service: YourModuleService):
        self.service = your_module_service

    async def process_data(self, param1: str, param2: int):
        params = select_keys(locals())
        return await self.service.get_data(**params)
```

### 4. Create the Router

```python
# your_module_router.py
from fastapi import APIRouter, Depends
from auth import owner_vat_check
from app.context.context import get_context
from app.context.utils import route_input

from .your_module_logic import YourModuleLogic
from .your_module_service import YourModuleService

router = APIRouter(prefix="/your-module", tags=["Your Module"])

get_params, get_logic = get_context(
    YourModuleLogic,
    YourModuleService,
)

@router.post("/endpoint", dependencies=[Depends(owner_vat_check)])
@route_input(include_body=True)
async def your_endpoint(
    params: dict = Depends(get_params),
    logic: YourModuleLogic = Depends(get_logic),
):
    return await logic.process_data(**params)
```

### 5. Register the Router

Add your router to `run.py`:

```python
from app.modules.your_module.your_module_router import router as your_module_router

# In create_api() function, add to the router list:
for router in (
    # ... existing routers
    your_module_router,
):
    api.include_router(router)
```


## Available Modules

The application currently includes the following modules:

- **Home**: Dashboard and overview data
- **User**: User management and authentication
- **Owner**: Owner/tenant management
- **Vessel**: Vessel information and management
- **Hull Performance**: Hull efficiency analysis
- **Efficiency**: Vessel efficiency calculations
- **Anomaly Detection**: Operational anomaly identification
- **MRV**: Monitoring, Reporting, and Verification compliance
- **CII**: Carbon Intensity Indicator calculations
- **Data Analytics**: Advanced analytics and reporting
- **Export**: Data export and reporting functionality
