from fastapi import APIRouter, Depends
from auth import owner_vat_check

from app.modules.mrv.logic_helpers.mrv_helper import Mrv<PERSON><PERSON>per
from app.modules.mrv.mrv_logic import MrvLogic
from app.modules.mrv.mrv_service import MrvService
from app.context.context import get_context
from app.context.utils import route_input

router = APIRouter(prefix="/mrv", tags=["MRV"])

get_mrv_params, get_mrv_logic = get_context(
    MrvLogic,
    MrvService,
    MrvHelper,
)


@router.get("", dependencies=[Depends(owner_vat_check)])
async def mrv_data(
    params: dict = Depends(get_mrv_params),
    logic: MrvLogic = Depends(get_mrv_logic),
):
    return await logic.mrv_data(**params)


@router.get("/report", dependencies=[Depends(owner_vat_check)])
async def mrv_report(
    params: dict = Depends(get_mrv_params),
    logic: MrvLogic = Depends(get_mrv_logic),
):
    return await logic.mrv_report(**params)


@router.get("/report/voyage", dependencies=[Depends(owner_vat_check)])
async def voyage_report(
    params: dict = Depends(get_mrv_params),
    logic: MrvLogic = Depends(get_mrv_logic),
):
    return await logic.voyage_report(**params)


@router.post("/voyage", dependencies=[Depends(owner_vat_check)])
@route_input(include_body=True)
async def create_voyage(
    params: dict = Depends(get_mrv_params),
    logic: MrvLogic = Depends(get_mrv_logic),
):
    return await logic.create_voyage(**params)


@router.put("/voyage", dependencies=[Depends(owner_vat_check)])
@route_input(include_body=True)
async def update_voyage(
        params: dict = Depends(get_mrv_params),
        logic: MrvLogic = Depends(get_mrv_logic),
):
    return await logic.update_voyage(**params)


@router.delete("/voyage", dependencies=[Depends(owner_vat_check)])
async def delete_voyage(
        params: dict = Depends(get_mrv_params),
        logic: MrvLogic = Depends(get_mrv_logic),
):
    return await logic.delete_voyage(**params)
