from app.services.redis_api.redis_api_client import RedisApi


class UserService:

    def __init__(self, redis_client: RedisApi = None):
        self.redis = redis_client or RedisApi()

    async def users_me(self, payload: dict):
        return await self.redis.call(
            path="get_user_by_email",
            method="POST",
            params=None,
            json=payload,
        )

    async def users(self, **params):
        return await self.redis.call(
            path="get_user_info_by_owner",
            key="result",
            **params
        )
