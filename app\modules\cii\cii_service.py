import json
from typing import Union, List

from app.services.redis_api.redis_api_client_error import RedisApiError
from app.services.redis_api.redis_api_client import RedisApi


class CiiService:

    def __init__(self, redis_client: RedisApi = None):
        self.redis = redis_client or RedisApi()

    async def get_cii_data(self, payload):
        return await self.redis.call(
            path="/cii",
            method="POST",
            json=payload,
        )

    async def get_cii_years(self, payload):
        return await self.redis.call(
            path="/cii-years",
            method="POST",
            json=payload
        )

    async def delete_key(self, key):
        return await self.redis.call(
            path="redis/delete",
            method="POST",
            json={"key": key},
        )

    async def scan_keys(self, key: str):
        return await self.redis.call(
            path="/redis/scan",
            pattern=key,
        )

    async def set_json_data(self, data: Union[dict, List[dict]]):
        """Set JSON data in redis_api

                Args:
                    data (Union[dict, List[dict]]): Key and value or list of keys and values dicts to set

                Raises:
                    RedisApiError: Error message from API
                    Exception: Other exceptions
                Returns:
                    int: Number of insertions

                """
        if isinstance(data, dict):
            data = [data]

        if not data:
            raise RedisApiError("No data provided")
        if not isinstance(data, dict) and not all("value" in item for item in data):
            raise RedisApiError("All items must have a value")

        try:
            payload = [
                {"key": item.get("key"), "value": json.dumps(item.get("value"))}
                for item in data
            ]
            return await self.redis.call(
                path="redis",
                key="inserts",
                method="POST",
                json=payload,
                expected_status=201
            )
        except Exception as e:
            raise RedisApiError("Error while setting json data: %s")
