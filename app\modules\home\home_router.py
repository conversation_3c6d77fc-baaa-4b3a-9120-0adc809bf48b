from fastapi import APIRouter, Depends
from auth import owner_vat_check

from app.general_logic_helper.controllers.data_controller import DataController
from app.general_logic_helper.handlers.measurements import Measurements
from app.modules.home.home_logic import HomeLogic
from app.modules.home.home_service import HomeService
from app.context.context import get_context

router = APIRouter(prefix="/home", tags=["Home"])

get_home_params, get_home_logic = get_context(
    HomeLogic,
    HomeService,
    DataController,
    Measurements,
)


@router.get("", dependencies=[Depends(owner_vat_check)])
async def home(
    params: dict = Depends(get_home_params),
    logic: HomeLogic = Depends(get_home_logic),
):
    return await logic.home(**params)


@router.get("/vessels", dependencies=[Depends(owner_vat_check)])
async def owners_with_vessel(
    params: dict = Depends(get_home_params),
    logic: HomeLogic = Depends(get_home_logic),
):
    return await logic.owners_with_vessel(**params)
