from fastapi import APIRouter, Depends
from auth import owner_vat_check

from app.modules.hull_performance.logic_helpers.hull_performance_helper import HullPerformanceHelper
from app.modules.hull_performance.hull_performance_logic import HullPerformanceLogic
from app.modules.hull_performance.hull_performance_service import HullPerformanceService
from app.context.context import get_context

router = APIRouter(tags=["Hull Performance"])

get_hull_params, get_hull_logic = get_context(
    HullPerformanceLogic,
    HullPerformanceService,
    HullPerformanceHelper,
)


@router.get("/hull-performance-ai", dependencies=[Depends(owner_vat_check)])
async def hull_ai(
    params: dict = Depends(get_hull_params),
    logic: HullPerformanceLogic = Depends(get_hull_logic),
):
    return await logic.hull_ai(**params)


@router.get("/hull-performance-raw", dependencies=[Depends(owner_vat_check)])
async def hull_raw(
    params: dict = Depends(get_hull_params),
    logic: HullPerformanceLogic = Depends(get_hull_logic),
):
    return await logic.hull_raw(**params)


@router.get("/request-search-hull", dependencies=[Depends(owner_vat_check)])
async def hull_search(
    params: dict = Depends(get_hull_params),
    logic: HullPerformanceLogic = Depends(get_hull_logic),
):
    return await logic.hull_search(**params)
