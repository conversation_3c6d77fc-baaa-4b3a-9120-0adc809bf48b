from typing import Any

from app.modules.anomaly_detection.anomaly_detection_service import AnomalyDetectionService
from app.context.utils import rename_vat


class AnomalyDetectionLogic:
    def __init__(self, anomaly_detection_service: AnomalyDetectionService):
        self.anomaly_detection_service = anomaly_detection_service

    async def anomaly_detection(self, owner_vat: str, vessel_imo: int) -> Any:
        return await self.anomaly_detection_service.anomaly_detection(rename_vat(**locals()))
