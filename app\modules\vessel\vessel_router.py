from fastapi import APIRouter, Depends
from auth import owner_vat_check

from app.modules.vessel.vessel_logic import VesselLogic
from app.modules.vessel.vessel_service import VesselService
from app.context.context import get_context

router = APIRouter(tags=["Vessels"])

get_vessel_params, get_vessel_logic = get_context(
    VesselLogic,
    VesselService,
)


@router.get("/vessels", dependencies=[Depends(owner_vat_check)])
async def vessels(
        params: dict = Depends(get_vessel_params),
        logic: VesselLogic = Depends(get_vessel_logic),
):
    return await logic.vessels(**params)
