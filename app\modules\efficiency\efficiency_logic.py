from fastapi import Body

from app.general_logic_helper.controllers.data_controller import DataController
from app.context.utils import extract_kwargs


class EfficiencyLogic:
    def __init__(self, data_controller: DataController):
        self.data_controller = data_controller

    async def efficiency_logic(self, request_data: dict = Body(...)):
        return await self.data_controller.calculate_efficiency_reports(**extract_kwargs(locals()))
