from app.services.redis_api.redis_api_client import RedisApi


# TODO test with frontend
class EngineHealthService:
    def __init__(self, redis_client: RedisApi = None):
        self.redis = redis_client or RedisApi()

    async def engine_health(self, **params):
        return await self.redis.call(
            path="engine_health",
            key="enginePerformance",
            **params
        )

