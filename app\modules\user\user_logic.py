from app.modules.user.user_service import UserService
from app.context.utils import select_keys


class UserLogic:
    def __init__(self, user_owner_service: UserService):
        self.user_owner_service = user_owner_service

    async def users_me(self, email: str):
        payload = {"email": email}
        return await self.user_owner_service.users_me(
            payload=payload,
        )

    async def users(self, owner_vat: str):
        return await self.user_owner_service.users(**select_keys(locals()))
