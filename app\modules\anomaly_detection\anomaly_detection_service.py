from app.services.redis_api.redis_api_client import RedisApi


class AnomalyDetectionService:
    def __init__(self, redis_client: RedisApi = None):
        self.redis = redis_client or RedisApi()

    async def anomaly_detection(self, payload):
        return await self.redis.call(
            path="anomaly_detection",
            key="anomaly_detection_data",
            method="POST",
            json=payload,
        )

