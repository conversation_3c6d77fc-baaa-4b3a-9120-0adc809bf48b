from app.modules.hull_performance.logic_helpers.hull_performance_helper import HullPerformanceHelper
from app.modules.hull_performance.hull_performance_service import HullPerformanceService
from app.context.utils import rename_vat


class HullPerformanceLogic:
    def __init__(self, hull_performance_service: HullPerformanceService, hull_performance_helper: HullPerformanceHelper):
        self.hull_performance_service = hull_performance_service
        self.hull_performance_helper = hull_performance_helper

    async def hull_ai(self, vessel_imo: str, owner_vat: str):
        return await self.hull_performance_service.hull_ai(**rename_vat(**locals()))

    async def hull_raw(self, vessel_imo: str, owner_vat: str):
        return await self.hull_performance_service.hull_raw(**rename_vat(**locals()))

    async def hull_search(
            self,
            vessel_imo: str,
            owner_vat: str,
            start_date: str,
            end_date: str,
    ):
        df = await self.hull_performance_helper.get_raw_data_in_period(
            vessel_imo, owner_vat, start_date, end_date
        )
        if df is not None:
            grouped = self.hull_performance_helper.group_data(df)
            processed_dict, speed_dict = self.hull_performance_helper.process_groups(grouped)
            resampled_data = df["shaft_1_power"].resample("12h").mean().dropna()
            added = self.hull_performance_helper.add_all_data(processed_dict, resampled_data)
            final = self.hull_performance_helper.add_speed_trendlines(speed_dict, added)
        else:
            final = {}

        df_ai = await self.hull_performance_helper.get_ai_data_in_period(
            vessel_imo, owner_vat, start_date, end_date
        )
        final["gages"] = self.hull_performance_helper.calculate_gages_value(df_ai)
        return final
