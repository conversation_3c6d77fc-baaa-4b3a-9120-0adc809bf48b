from fastapi import APIRouter, Depends
from auth import owner_vat_check

from app.general_logic_helper.controllers.data_controller import DataController
from app.modules.efficiency.efficiency_logic import EfficiencyLogic
from app.context.context import get_context
from app.context.utils import route_input

router = APIRouter(tags=["Efficiency"])

get_efficiency_params, get_efficiency_logic = get_context(
    EfficiencyLogic,
    None,
    DataController,
)


@router.post("/efficiency", dependencies=[Depends(owner_vat_check)])
@route_input(include_body=True)
async def efficiency_logic(
    params: dict = Depends(get_efficiency_params),
    logic: EfficiencyLogic = Depends(get_efficiency_logic),
):
    return await logic.efficiency_logic(**params)
