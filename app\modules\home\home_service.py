from app.services.redis_api.redis_api_client import RedisApi


class HomeService:

    def __init__(self, redis_client: RedisApi = None):
        self.redis = redis_client or RedisApi()

    async def owners_with_vessel(self, **params):
        return await self.redis.call(
            path="get_owner_fleet_data",
            **params,
        )

    async def home(self, payload):
        return await self.redis.call(
            path="home",
            key="home_page_data",
            method="POST",
            params=None,
            json=payload,
        )
