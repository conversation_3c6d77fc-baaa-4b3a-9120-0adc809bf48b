from app.services.redis_api.redis_api_client import RedisApi


class HullPerformanceService:
    def __init__(self, redis_client: RedisApi = None):
        self.redis = redis_client or RedisApi()

    async def hull_ai(self, **params):
        return await self.redis.call(
            path="hull_performance_ai",
            key="performance",
            **params
        )

    async def hull_raw(self, **params):
        return await self.redis.call(
            path="hull_performance_raw",
            key="manual_hull",
            **params
        )
