from fastapi import APIRouter, Depends

from app.context.utils import route_input
from auth import owner_vat_check
from app.modules.user.user_logic import UserLogic
from app.modules.user.user_service import UserService
from app.context.context import get_context

router = APIRouter(prefix="/users", tags=["Users"])

get_user_params, get_user_logic = get_context(
    UserLogic,
    UserService,
)


@router.get("", dependencies=[Depends(owner_vat_check)])
async def users(
        params: dict = Depends(get_user_params),
        logic: UserLogic = Depends(get_user_logic),
):
    return await logic.users(**params)


@router.get("/me", dependencies=[Depends(owner_vat_check)])
@route_input(headers_to_include={"X-User-Email": "email"})    # Get this name from settings .env
async def users_me(
    params: dict = Depends(get_user_params),
    logic: UserLogic = Depends(get_user_logic),
):
    return await logic.users_me(**params)
