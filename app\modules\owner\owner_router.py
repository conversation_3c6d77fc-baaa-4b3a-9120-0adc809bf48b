from fastapi import APIRouter, Depends

from app.context.utils import route_input
from app.modules.owner.owner_logic import OwnerLogic
from app.modules.owner.owner_service import OwnerService
from app.context.context import get_context
from app.config.settings import settings

router = APIRouter(tags=["Owners"])

get_owner_params, get_owner_logic = get_context(
    OwnerLogic,
    OwnerService,
)


@router.get("/owners")
@route_input(headers_to_include={settings.session_owner_vat: "session_owner_vat"})
async def owners(
    params: dict = Depends(get_owner_params),
    logic: OwnerLogic = Depends(get_owner_logic),
):
    return await logic.owners(**params)
